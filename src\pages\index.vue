<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
function handleClick() {
  Message.info('This is an info message')
}
</script>

<template>
  <a-row class="main p-16px" :gutter="16">
    <a-col :span="16">
      <div class="box">
        <div class="hd flex items-center gap-x-8px">
          <icon-user :size="16" />
          <span class="text-16px font-bold">直播账号</span>
        </div>
        <div class="bd m-t-8px">
          <Account />
        </div>
      </div>
      <div class="box m-t-16px">
        <div class="hd flex items-center justify-between gap-x-8px">
          <div class="flex items-center gap-x-8px">
            <icon-list :size="16" />
            <span class="text-16px font-bold">计划商品</span>
          </div>
          <div class="flex items-center gap-x-8px">
            <a-button type="primary" size="mini" @click="handleClick">
              <span>一键同步</span>
            </a-button>
          </div>
        </div>
        <div class="bd m-t-8px">
          <Goods />
        </div>
      </div>
      <div class="box m-t-16px">
        <div class="hd flex items-center gap-x-8px">
          <icon-history :size="16" />
          <span class="text-16px font-bold">运行日志</span>
        </div>
        <div class="bd bg-white m-t-8px">
          <Logs />
        </div>
      </div>
    </a-col>
    <a-col :span="8">
      <div class="box">
        <div class="hd flex items-center gap-x-8px">
          <icon-pen :size="16" />
          <span class="text-16px font-bold">单品维护</span>
        </div>
        <div class="bd m-t-8px">
          <Account />
        </div>
      </div>
      <div class="box m-t-16px">
        <div class="hd flex items-center gap-x-8px">
          <icon-gift :size="16" />
          <span class="text-16px font-bold">福利品</span>
        </div>
        <div class="bd m-t-8px">
          <Account />
        </div>
      </div>
      <div class="box m-t-16px">
        <div class="hd flex items-center gap-x-8px">
          <icon-gift :size="16" />
          <span class="text-16px font-bold">非福利品</span>
        </div>
        <div class="bd m-t-8px">
          
        </div>
      </div>
    </a-col>
  </a-row>
</template>

<style scoped lang="less"></style>