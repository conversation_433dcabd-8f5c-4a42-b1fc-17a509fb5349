<script setup lang="ts">
import { reactive, ref } from 'vue';

export default {
const columns = reactive([
  {
    title: 'Name',
    dataIndex: 'name',
  },
  {
    title: '<PERSON><PERSON>',
    dataIndex: 'salary',
  },
  {
    title: 'Address',
    dataIndex: 'address',
  },
  {
    title: 'Email',
    dataIndex: 'email',
  },
])

const data = ref([{
  key: '1',
  name: '<PERSON>',
  salary: 23000,
  address: '32 Park Road, London',
  email: '<EMAIL>'
}, {
  key: '2',
  name: '<PERSON><PERSON>',
  salary: 25000,
  address: '35 Park Road, London',
  email: '<EMAIL>'
}, {
  key: '3',
  name: '<PERSON>',
  salary: 22000,
  address: '31 Park Road, London',
  email: '<EMAIL>'
}, {
  key: '4',
  name: '<PERSON>',
  salary: 17000,
  address: '42 Park Road, London',
  email: '<EMAIL>'
}, {
  key: '5',
  name: '<PERSON>',
  salary: 27000,
  address: '62 Park Road, London',
  email: 'will<PERSON>.<EMAIL>'
}])

const handleChange = (_data) => {
  console.log(_data);
  data.value = _data
}
</script>

<template>
  <a-table :columns="columns" :data="data" @change="handleChange" :draggable="{ type: 'handle', width: 40 }" />
</template>

<style scoped lang="less"></style>