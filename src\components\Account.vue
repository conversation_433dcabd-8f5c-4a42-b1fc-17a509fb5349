<script setup lang="ts">
import { reactive, ref } from 'vue';

const columns = reactive([
  {
    title: '直播平台',
    dataIndex: 'platform',
  },
  {
    title: '小店状态',
    dataIndex: 'shop',
  },
  {
    title: '助手状态',
    dataIndex: 'helper',
  },
  {
    title: '直播计划',
    dataIndex: 'plan',
  },
  {
    title: '商品同步',
    dataIndex: 'sync',
  },
])

const data = ref([
  {
    key: '1',
    platform: '微信',
    logo: '/weixin.svg',
    shop: false,
    helper: false,
    plan: [],
    sync: false,
    status: 1,
  },
  {
    key: '2',
    platform: '抖音',
    logo: '/douyin.svg',
    shop: false,
    helper: false,
    plan: [],
    sync: false,
    status: 1,
  },
  {
    key: '3',
    platform: '快手',
    logo: '/kuaishou.svg',
    shop: false,
    helper: false,
    plan: [],
    sync: false,
    status: 1,
  },
  {
    key: '4',
    platform: '小红书',
    logo: '/xiaohongshu.svg',
    shop: false,
    helper: false,
    plan: [],
    sync: false,
    status: 1,
  }
])

const handleChange = (_data: any) => {
  console.log(_data);
  data.value = _data
}
</script>

<template>
  <a-table :columns="columns" :data="data" :pagination="false" @change="handleChange" :draggable="{ type: 'handle', width: 40 }" />
</template>

<style scoped lang="less"></style>