/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Account: typeof import('./components/Account.vue')['default']
    Components: typeof import('./components/index.vue')['default']
    copy: typeof import('./components/DarkToggle copy.vue')['default']
    DarkToggle: typeof import('./components/TitleBar/DarkToggle.vue')['default']
    GlobalSetting: typeof import('./components/TitleBar/GlobalSetting.vue')['default']
    HeaderBar: typeof import('./components/HeaderBar.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TitleBar: typeof import('./components/TitleBar.vue')['default']
  }
}
